/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #1a1a1a;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: '<PERSON>', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

h3 {
    font-size: 2rem;
    margin-bottom: 0.8rem;
}

h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

/* Buttons */
.more-btn {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    padding: 12px 30px;
    font-family: '<PERSON>', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 3px;
}

.more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 2px solid #ff6b35;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-logo img {
    width: 40px;
    height: 40px;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-main {
    font-family: 'Oswald', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff6b35;
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1;
}

.logo-sub {
    font-size: 0.8rem;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #fff;
    text-decoration: none;
    font-family: 'Oswald', sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #ff6b35;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #ff6b35;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #fff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
}

.hero-pattern {
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, #ff6b35 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, #f7931e 2px, transparent 2px);
    background-size: 50px 50px;
    animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    padding: 0 20px;
    z-index: 2;
}

.hero-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-left: 4px solid #ff6b35;
    backdrop-filter: blur(10px);
    border-radius: 5px;
}

.stat-number {
    font-family: 'Oswald', sans-serif;
    font-size: 3rem;
    font-weight: 700;
    color: #ff6b35;
    min-width: 80px;
}

.stat-text {
    color: #ccc;
    font-size: 0.9rem;
    line-height: 1.4;
}

.hero-infographic {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    position: relative;
}

.infographic-elements {
    position: relative;
    width: 300px;
    height: 300px;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 80px;
    height: 80px;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.element-2 {
    width: 60px;
    height: 60px;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation-delay: 1.5s;
}

.element-3 {
    width: 70px;
    height: 70px;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 3s;
}

.element-4 {
    width: 50px;
    height: 50px;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-20px) scale(1.1); }
}

/* About Section */
.about {
    padding: 100px 0;
    background: #fff;
    color: #333;
}

.about-header {
    text-align: center;
    margin-bottom: 4rem;
}

.about-header h2 {
    color: #333;
    margin-bottom: 1rem;
}

.about-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.about-tabs {
    max-width: 1000px;
    margin: 0 auto;
}

.tab-buttons {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    border-bottom: 2px solid #eee;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    font-family: 'Oswald', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #666;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #ff6b35;
}

.tab-btn::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 3px;
    background: #ff6b35;
    transition: width 0.3s ease;
}

.tab-btn.active::after {
    width: 100%;
}

.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-pane h3 {
    color: #333;
    margin-bottom: 1.5rem;
}

.tab-pane p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.customizable-features {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.studio-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.studio-item {
    padding: 2rem;
    background: #f9f9f9;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.studio-item:hover {
    transform: translateY(-5px);
}

.studio-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.studio-item h4 {
    color: #333;
    margin-bottom: 1rem;
}

.studio-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.6;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.team-member {
    text-align: center;
    padding: 2rem;
    background: #f9f9f9;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-avatar {
    position: relative;
    margin-bottom: 1.5rem;
}

.member-avatar img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
}

.member-social {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.member-avatar:hover .member-social {
    opacity: 1;
}

.member-social a {
    width: 30px;
    height: 30px;
    background: rgba(255, 107, 53, 0.9);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.member-social a:hover {
    transform: scale(1.1);
}

.member-info h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.member-info span {
    color: #ff6b35;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Skills Section */
.skills {
    padding: 100px 0;
    background: #2d2d2d;
    color: white;
}

.skills-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.skills-text h2 {
    font-size: 4rem;
    color: #ff6b35;
    margin-bottom: 1rem;
}

.skills-text p {
    color: #ccc;
    font-size: 1.1rem;
    line-height: 1.8;
}

.skills-chart {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.skill-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.skill-number {
    font-family: 'Oswald', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: #ff6b35;
}

.skill-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    width: 0;
    transition: width 2s ease-in-out;
    border-radius: 4px;
}

.skill-label {
    font-family: 'Oswald', sans-serif;
    font-weight: 600;
    color: #ccc;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Blog Section */
.blog {
    padding: 100px 0;
    background: #f5f5f5;
}

.blog-post {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.post-image {
    height: 300px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    padding: 2rem;
}

.post-overlay h3 {
    color: white;
    margin-bottom: 1rem;
}

.post-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #ccc;
}

.post-content p {
    color: #666;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.post-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stat {
    padding: 0.5rem 1rem;
    background: #ff6b35;
    color: white;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.read-more {
    color: #ff6b35;
    text-decoration: none;
    font-family: 'Oswald', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #f7931e;
}

/* Mobile Section */
.mobile {
    padding: 100px 0;
    background: #1a1a1a;
    color: white;
    text-align: center;
}

.mobile-content h2 {
    color: #ff6b35;
    margin-bottom: 2rem;
    font-size: 2.5rem;
}

.mobile-content p {
    color: #ccc;
    font-size: 1.1rem;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.mobile-features {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.mobile-feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.mobile-feature:hover {
    transform: translateY(-5px);
}

.mobile-feature i {
    font-size: 3rem;
    color: #ff6b35;
}

.mobile-feature span {
    font-family: 'Oswald', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: #fff;
    position: relative;
}

.features-header {
    text-align: center;
    margin-bottom: 4rem;
}

.features-header h2 {
    color: #333;
    margin-bottom: 1rem;
}

.features-header h3 {
    color: #ff6b35;
    margin-bottom: 2rem;
    font-weight: 400;
}

.features-header p {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
    margin-bottom: 4rem;
}

.feature-item {
    display: flex;
    gap: 2rem;
    padding: 2rem;
    background: #f9f9f9;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    flex-shrink: 0;
}

.feature-item h4 {
    color: #333;
    margin-bottom: 1rem;
}

.feature-item p {
    color: #666;
    line-height: 1.6;
}

.result-badge {
    position: absolute;
    top: 50%;
    right: 5%;
    transform: translateY(-50%);
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
}

.result-text {
    font-family: 'Oswald', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
}

.result-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Framework Section */
.framework {
    padding: 100px 0;
    background: #2d2d2d;
    color: white;
}

.framework-header {
    text-align: center;
    margin-bottom: 4rem;
}

.framework-header h2 {
    color: #ff6b35;
    margin-bottom: 1rem;
}

.framework-header p {
    color: #ccc;
    font-size: 1.2rem;
}

.framework-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
    margin-bottom: 4rem;
}

.framework-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.framework-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.framework-item:hover {
    transform: translateX(10px);
}

.framework-item h4 {
    color: white;
    flex: 1;
    margin-right: 1rem;
}

.framework-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.framework-description {
    text-align: center;
    margin-bottom: 4rem;
}

.framework-description p {
    color: #ccc;
    font-size: 1.1rem;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto 2rem;
}

.framework-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.framework-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.framework-graphic {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 50%;
    position: relative;
    animation: rotate 10s linear infinite;
}

.framework-graphic::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    background: #2d2d2d;
    border-radius: 50%;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.framework-text p {
    color: #ccc;
    line-height: 1.8;
    margin-bottom: 2rem;
}

/* Stats Section */
.stats {
    padding: 80px 0;
    background: #1a1a1a;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.stat-counter {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.stat-counter:hover {
    transform: translateY(-10px);
}

.counter-number {
    display: block;
    font-family: 'Oswald', sans-serif;
    font-size: 4rem;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 1rem;
}

.counter-label {
    font-family: 'Oswald', sans-serif;
    font-weight: 600;
    color: #ccc;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* Facts Section */
.facts {
    padding: 100px 0;
    background: #fff;
    text-align: center;
}

.facts-header h2 {
    color: #333;
    margin-bottom: 1rem;
}

.facts-header p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 4rem;
}

.facts-skills {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.fact-skill {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.skill-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(#ff6b35 0deg, #ff6b35 var(--percent), #eee var(--percent), #eee 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 1rem;
}

.skill-circle::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
}

.skill-name {
    position: relative;
    z-index: 1;
    font-family: 'Oswald', sans-serif;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Purchase Section */
.purchase {
    padding: 100px 0;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    text-align: center;
}

.purchase h2 {
    color: white;
    font-size: 3rem;
    margin-bottom: 2rem;
}

.purchase p {
    font-size: 1.2rem;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto;
}

/* Footer */
.footer {
    padding: 50px 0;
    background: #1a1a1a;
    color: white;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-logo .logo-text {
    display: flex;
    flex-direction: column;
}

.footer-text p {
    color: #999;
    font-size: 0.9rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: rgba(255, 107, 53, 0.2);
    color: #ff6b35;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: #ff6b35;
    color: white;
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .skills-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .blog-post {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .framework-info {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .result-badge {
        position: static;
        margin: 2rem auto 0;
        transform: none;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero {
        padding: 0 20px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .tab-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .studio-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .skills-chart {
        grid-template-columns: 1fr;
    }

    .mobile-features {
        flex-direction: column;
        align-items: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .framework-features {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .facts-skills {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    h2 {
        font-size: 2rem;
    }

    .hero-stats {
        gap: 1rem;
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
        min-width: auto;
    }

    .skills-text h2 {
        font-size: 3rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .counter-number {
        font-size: 3rem;
    }
}
