// Lunar Calendar Conversion Functions
class LunarCalendar {
    constructor() {
        // <PERSON><PERSON><PERSON><PERSON> can (10 elements)
        this.heavenlyStems = ['<PERSON>i<PERSON><PERSON>', 'Ất', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Kỷ', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>u<PERSON>'];
        
        // Địa chi (12 elements)
        this.earthlyBranches = ['T<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Th<PERSON><PERSON>', 'Tỵ', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>h<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ợ<PERSON>'];
        
        // Zodiac animals
        this.zodiacAnimals = ['<PERSON><PERSON><PERSON>', 'Trâ<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ắ<PERSON>', '<PERSON>ự<PERSON>', '<PERSON><PERSON>', 'Khỉ', '<PERSON><PERSON>', 'Ch<PERSON>', 'He<PERSON>'];
        
        // Lunar months in Vietnamese
        this.lunarMonths = [
            'Tháng <PERSON>', '<PERSON>h<PERSON><PERSON>', '<PERSON>h<PERSON>g <PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>háng <PERSON>ời Một', 'Tháng <PERSON>ạp'
        ];
        
        // Good hours for each day
        this.goodHours = [
            ['Tý', 'Sửu', 'Thìn', 'Mùi'],
            ['Dần', 'Mão', 'Tỵ', 'Thân'],
            ['Tý', 'Thìn', 'Ngọ', 'Dậu'],
            ['Sửu', 'Tỵ', 'Mùi', 'Tuất'],
            ['Dần', 'Ngọ', 'Thân', 'Hợi'],
            ['Mão', 'Mùi', 'Dậu', 'Tý'],
            ['Thìn', 'Thân', 'Tuất', 'Sửu']
        ];
        
        // Stars for each day
        this.stars = [
            'Thiên Đức', 'Thiên Quan', 'Thiên Tài', 'Thiên Thọ', 'Thiên Phúc',
            'Thiên Hỷ', 'Thiên Ân', 'Thiên Quý', 'Thiên Lộc', 'Thiên Ma'
        ];
    }

    // Convert solar date to lunar date (simplified calculation)
    solarToLunar(solarDate) {
        const year = solarDate.getFullYear();
        const month = solarDate.getMonth() + 1;
        const day = solarDate.getDate();
        
        // This is a simplified conversion - in real implementation, 
        // you would use more complex astronomical calculations
        const lunarOffset = this.getLunarOffset(year, month);
        let lunarDay = day - lunarOffset;
        let lunarMonth = month;
        let lunarYear = year;
        
        if (lunarDay <= 0) {
            lunarMonth--;
            if (lunarMonth <= 0) {
                lunarMonth = 12;
                lunarYear--;
            }
            lunarDay += this.getLunarMonthDays(lunarYear, lunarMonth);
        }
        
        const maxDays = this.getLunarMonthDays(lunarYear, lunarMonth);
        if (lunarDay > maxDays) {
            lunarDay -= maxDays;
            lunarMonth++;
            if (lunarMonth > 12) {
                lunarMonth = 1;
                lunarYear++;
            }
        }
        
        return {
            day: lunarDay,
            month: lunarMonth,
            year: lunarYear
        };
    }
    
    // Get lunar offset for conversion (simplified)
    getLunarOffset(year, month) {
        // Simplified offset calculation
        const baseOffset = 18; // Average offset
        const yearOffset = (year - 2024) * 11; // Approximate yearly drift
        const monthOffset = [0, 1, -1, 0, 1, 0, 1, 1, 0, 1, 0, 1][month - 1];
        
        return baseOffset + (yearOffset % 30) + monthOffset;
    }
    
    // Get number of days in lunar month
    getLunarMonthDays(year, month) {
        // Simplified - alternates between 29 and 30 days
        const isLeapYear = this.isLunarLeapYear(year);
        const baseDays = (month % 2 === 1) ? 30 : 29;
        
        // Adjust for leap year
        if (isLeapYear && month === 7) {
            return baseDays + 1;
        }
        
        return baseDays;
    }
    
    // Check if lunar year is leap year
    isLunarLeapYear(year) {
        // Simplified leap year calculation
        return (year % 3 === 0);
    }
    
    // Get Can Chi for year
    getYearCanChi(year) {
        const stemIndex = (year - 4) % 10;
        const branchIndex = (year - 4) % 12;
        return this.heavenlyStems[stemIndex] + ' ' + this.earthlyBranches[branchIndex];
    }
    
    // Get zodiac animal for year
    getZodiacAnimal(year) {
        const animalIndex = (year - 4) % 12;
        return this.zodiacAnimals[animalIndex];
    }
    
    // Get Can Chi for day
    getDayCanChi(date) {
        // Calculate days since a reference date
        const referenceDate = new Date(1900, 0, 1);
        const daysDiff = Math.floor((date - referenceDate) / (1000 * 60 * 60 * 24));
        
        const stemIndex = (daysDiff + 9) % 10; // Adjust for reference
        const branchIndex = (daysDiff + 1) % 12; // Adjust for reference
        
        return this.heavenlyStems[stemIndex] + ' ' + this.earthlyBranches[branchIndex];
    }
    
    // Get good hours for a date
    getGoodHours(date) {
        const dayOfWeek = date.getDay();
        return this.goodHours[dayOfWeek].join(', ');
    }
    
    // Get star for a date
    getStar(date) {
        const dayOfYear = this.getDayOfYear(date);
        const starIndex = dayOfYear % this.stars.length;
        return this.stars[starIndex];
    }
    
    // Get day of year
    getDayOfYear(date) {
        const start = new Date(date.getFullYear(), 0, 0);
        const diff = date - start;
        return Math.floor(diff / (1000 * 60 * 60 * 24));
    }
    
    // Format lunar date
    formatLunarDate(lunarDate) {
        return {
            day: lunarDate.day,
            monthName: this.lunarMonths[lunarDate.month - 1],
            year: lunarDate.year,
            fullDate: `${lunarDate.day} ${this.lunarMonths[lunarDate.month - 1]} ${lunarDate.year}`
        };
    }
    
    // Get lunar month name with year
    getLunarMonthYear(lunarDate) {
        const canChi = this.getYearCanChi(lunarDate.year);
        return `${this.lunarMonths[lunarDate.month - 1]} ${canChi}`;
    }
    
    // Get complete lunar information for a date
    getLunarInfo(solarDate) {
        const lunarDate = this.solarToLunar(solarDate);
        const formatted = this.formatLunarDate(lunarDate);
        
        return {
            lunar: lunarDate,
            formatted: formatted,
            canChi: this.getDayCanChi(solarDate),
            yearCanChi: this.getYearCanChi(lunarDate.year),
            zodiac: this.getZodiacAnimal(lunarDate.year),
            goodHours: this.getGoodHours(solarDate),
            star: this.getStar(solarDate),
            monthYear: this.getLunarMonthYear(lunarDate)
        };
    }
}

// Export for use in main script
window.LunarCalendar = LunarCalendar;
