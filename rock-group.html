<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rock Group - Infographic Theme</title>
    <link rel="stylesheet" href="rock-group.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Oswald:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <img
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzMzMzMzMyIvPgo8cGF0aCBkPSJNMTIgMTJIMjhWMjhIMTJWMTJaIiBzdHJva2U9IiNGRkZGRkYiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSI0IiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo="
            alt="Rock Group"
          />
          <div class="logo-text">
            <span class="logo-main">rock group</span>
            <span class="logo-sub">infographic theme</span>
          </div>
        </div>
        <ul class="nav-menu">
          <li><a href="#features">Features</a></li>
          <li><a href="#about">About Us</a></li>
          <li><a href="#blog">Blog</a></li>
          <li><a href="#portfolio">Portfolio</a></li>
        </ul>
        <div class="nav-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-bg">
        <div class="hero-pattern"></div>
      </div>
      <div class="hero-content">
        <div class="hero-stats">
          <div class="stat-item">
            <span class="stat-number">50</span>
            <span class="stat-text"
              >Aliquam porta dui nec dapibus. Maecenas molestie ultrices
              blandit.</span
            >
          </div>
          <div class="stat-item">
            <span class="stat-number">78</span>
            <span class="stat-text">Maecenas molestie ultrices blandit.</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">95</span>
            <span class="stat-text">Maecenas molestie ultrices blandit.</span>
          </div>
        </div>
        <div class="hero-infographic">
          <div class="infographic-elements">
            <div class="element element-1"></div>
            <div class="element element-2"></div>
            <div class="element element-3"></div>
            <div class="element element-4"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
      <div class="container">
        <div class="about-header">
          <h2>Who we are</h2>
          <p>
            Professional team of ThemeRex studio presents this terrific
            Infographic Theme
          </p>
        </div>
        <div class="about-tabs">
          <div class="tab-buttons">
            <button class="tab-btn active" data-tab="customizable">
              Fully Customizable
            </button>
            <button class="tab-btn" data-tab="studio">About Studio</button>
            <button class="tab-btn" data-tab="team">Project Team</button>
          </div>
          <div class="tab-content">
            <div class="tab-pane active" id="customizable">
              <h3>FULLY Customizable</h3>
              <p>
                RockGroup is an Infographic theme that offers infinite options
                for creation of the unique layout for your website.
              </p>
              <div class="customizable-features">
                <div class="feature-icon">
                  <i class="fas fa-cogs"></i>
                </div>
              </div>
              <p>
                We created three popular infographic skins for business, ecology
                and travel. You may use these templates both for these themes
                and for many others, since they are quite versatile.
              </p>
            </div>
            <div class="tab-pane" id="studio">
              <h3>About STUDIO</h3>
              <div class="studio-grid">
                <div class="studio-item">
                  <div class="studio-icon">
                    <i class="fas fa-heart"></i>
                  </div>
                  <h4>With Love to Customers</h4>
                  <p>
                    ThemeRex is a studio that aims to make their user's
                    experience easier and much more pleasant. You probably won't
                    have a better opportunity to make sure of their competence,
                    as well as friendliness towards their customers.
                  </p>
                </div>
                <div class="studio-item">
                  <div class="studio-icon">
                    <i class="fas fa-star"></i>
                  </div>
                  <h4>High Professionalism</h4>
                  <p>
                    Many year experience and hundreds of successful projects
                    have made ThemeRex a professional on the web area. Within
                    this period of time, the company has gone through a serious
                    evolution from amateurs to leaders.
                  </p>
                </div>
                <div class="studio-item">
                  <div class="studio-icon">
                    <i class="fas fa-target"></i>
                  </div>
                  <h4>Serving Clients' Goals</h4>
                  <p>
                    ThemeRex is doing their best to grant each owner of their
                    themes with maximum opportunities to present their
                    individuality, show their achievements, and establish the
                    best contact with their audience.
                  </p>
                </div>
                <div class="studio-item">
                  <div class="studio-icon">
                    <i class="fas fa-shield-alt"></i>
                  </div>
                  <h4>Reliability and Prestige</h4>
                  <p>
                    Purchasing products from ThemeRex means entrusting your
                    reputation to one of the best web-studios. ThemeRex company
                    does its work with its customer in mind.
                  </p>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="team">
              <h3>Project TEAM</h3>
              <div class="team-grid">
                <div class="team-member">
                  <div class="member-avatar">
                    <img
                      src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiNEREREREQiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjOTk5OTk5Ij4KPHA+VGVhbSBNZW1iZXI8L3A+Cjwvc3ZnPgo8L3N2Zz4K"
                      alt="Trinity"
                    />
                    <div class="member-social">
                      <a href="#"><i class="fab fa-facebook"></i></a>
                      <a href="#"><i class="fab fa-twitter"></i></a>
                      <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                  </div>
                  <div class="member-info">
                    <h4>Trinity</h4>
                    <span>Web Designer</span>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <img
                      src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiNEREREREQiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjOTk5OTk5Ij4KPHA+VGVhbSBNZW1iZXI8L3A+Cjwvc3ZnPgo8L3N2Zz4K"
                      alt="Katherine"
                    />
                    <div class="member-social">
                      <a href="#"><i class="fab fa-facebook"></i></a>
                      <a href="#"><i class="fab fa-twitter"></i></a>
                      <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                  </div>
                  <div class="member-info">
                    <h4>Katherine</h4>
                    <span>Web Developer</span>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <img
                      src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiNEREREREQiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjOTk5OTk5Ij4KPHA+VGVhbSBNZW1iZXI8L3A+Cjwvc3ZnPgo8L3N2Zz4K"
                      alt="Henry"
                    />
                    <div class="member-social">
                      <a href="#"><i class="fab fa-facebook"></i></a>
                      <a href="#"><i class="fab fa-twitter"></i></a>
                      <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                  </div>
                  <div class="member-info">
                    <h4>Henry</h4>
                    <span>Project Manager</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button class="more-btn">more</button>
        </div>
      </div>
    </section>

    <!-- Skills Section -->
    <section class="skills">
      <div class="container">
        <div class="skills-content">
          <div class="skills-text">
            <h2>6231</h2>
            <p>
              ...cups of coffee was consumed by our team in 2014. And yes, this
              figure here is for no reason. And we are presenting you trx skills
              widget specially stylized for rock group
            </p>
          </div>
          <div class="skills-chart">
            <div class="skill-item">
              <div class="skill-number">5474</div>
              <div class="skill-bar">
                <div class="skill-progress" data-percent="90"></div>
              </div>
              <div class="skill-label">L1</div>
            </div>
            <div class="skill-item">
              <div class="skill-number">856</div>
              <div class="skill-bar">
                <div class="skill-progress" data-percent="60"></div>
              </div>
              <div class="skill-label">L2</div>
            </div>
            <div class="skill-item">
              <div class="skill-number">3568</div>
              <div class="skill-bar">
                <div class="skill-progress" data-percent="80"></div>
              </div>
              <div class="skill-label">L3</div>
            </div>
            <div class="skill-item">
              <div class="skill-number">2354</div>
              <div class="skill-bar">
                <div class="skill-progress" data-percent="70"></div>
              </div>
              <div class="skill-label">L4</div>
            </div>
            <div class="skill-item">
              <div class="skill-number">6231</div>
              <div class="skill-bar">
                <div class="skill-progress" data-percent="95"></div>
              </div>
              <div class="skill-label">L5</div>
            </div>
            <div class="skill-item">
              <div class="skill-number">1256</div>
              <div class="skill-bar">
                <div class="skill-progress" data-percent="50"></div>
              </div>
              <div class="skill-label">L6</div>
            </div>
          </div>
        </div>
        <button class="more-btn">more</button>
      </div>
    </section>

    <!-- Blog Section -->
    <section class="blog" id="blog">
      <div class="container">
        <div class="blog-post">
          <div class="post-image">
            <div class="post-overlay">
              <h3>Skills Widget</h3>
              <div class="post-meta">
                <span>Posted February 25, 2014</span>
                <span>by TRX_admin</span>
                <span>in Post Formats, Post formats fullwidth</span>
              </div>
            </div>
          </div>
          <div class="post-content">
            <p>
              To display your knowledge and skills graphically, you have a
              specially designed widget at your disposal, such as "TRX Skills".
              You may choose unlimited number of skills, name them, set the
              level, color and one of the display styles.
            </p>
            <div class="post-stats">
              <span class="stat">More</span>
              <span class="stat">Share</span>
              <span class="stat">153</span>
              <span class="stat">1</span>
              <span class="stat">79.3</span>
              <span class="stat">18</span>
            </div>
            <a href="#" class="read-more">read more</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Mobile Section -->
    <section class="mobile">
      <div class="container">
        <div class="mobile-content">
          <h2>rock is easily adaptable to any mobile device</h2>
          <p>
            The fact of prevalence of mobile devices obliges to adjust any
            website to their peculiarity at maximum degree. We have done our
            best for your website based on p.xel to blend with this trend.
          </p>
          <div class="mobile-features">
            <div class="mobile-feature">
              <i class="fas fa-eye"></i>
              <span>Retina Ready</span>
            </div>
            <div class="mobile-feature">
              <i class="fas fa-tablet-alt"></i>
              <span>Tablet friendly</span>
            </div>
            <div class="mobile-feature">
              <i class="fas fa-mobile-alt"></i>
              <span>100% responsive</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
      <div class="container">
        <div class="features-header">
          <h2>what we do</h2>
          <h3>more information about rock group features</h3>
          <p>
            ThemeRex worked out an amazing combination of vast functionality and
            user's comfort. Thanks to an amazingly user friendly TRX Framework,
            customers have a chance to customize their theme the way they like.
          </p>
        </div>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-mobile-alt"></i>
            </div>
            <h4>100% Responsive</h4>
            <p>
              The fact of prevalence of mobile devices obliges to adjust any
              actual website to their peculiarity at maximum degree.
            </p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-life-ring"></i>
            </div>
            <h4>Free Support</h4>
            <p>
              Our Support Team is ready to become your reliable guide in the
              World of RockGroup. We provide free access to theme Documentation
              and our Ticket Sys.
            </p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-code"></i>
            </div>
            <h4>Shortcodes</h4>
            <p>
              Rock Group includes the most full set of Shortcodes, amongst which
              you can find those you can get only in ThemeRex products.
            </p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-search"></i>
            </div>
            <h4>SEO Ready</h4>
            <p>
              HTML5 code integrated into the theme meets the best SEO
              approaches. The theme supports compatibility with advanced SEO
              plugins.
            </p>
          </div>
        </div>
        <div class="result-badge">
          <span class="result-text">100%</span>
          <span class="result-label">result</span>
        </div>
      </div>
    </section>

    <!-- Framework Section -->
    <section class="framework">
      <div class="container">
        <div class="framework-header">
          <h2>trx framework</h2>
          <p>Combination of vast functionality and user's comfort!</p>
        </div>
        <div class="framework-content">
          <div class="framework-features">
            <div class="framework-column">
              <div class="framework-item">
                <h4>Unique Setting Inheritance and Override System</h4>
                <div class="framework-icon">
                  <i class="fas fa-cogs"></i>
                </div>
              </div>
              <div class="framework-item">
                <h4>Shortcode Builder or Visual Composer</h4>
                <div class="framework-icon">
                  <i class="fas fa-puzzle-piece"></i>
                </div>
              </div>
              <div class="framework-item">
                <h4>Rating \ Review Mechanism and Media Content Manager</h4>
                <div class="framework-icon">
                  <i class="fas fa-star"></i>
                </div>
              </div>
            </div>
            <div class="framework-column">
              <div class="framework-item">
                <h4>Rating \ Review Mechanism and Media Content Manager</h4>
                <div class="framework-icon">
                  <i class="fas fa-star"></i>
                </div>
              </div>
              <div class="framework-item">
                <h4>PO Composer</h4>
                <div class="framework-icon">
                  <i class="fas fa-edit"></i>
                </div>
              </div>
              <div class="framework-item">
                <h4>Unique Setting Inheritance and Override System</h4>
                <div class="framework-icon">
                  <i class="fas fa-cogs"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="framework-description">
          <p>
            Common settings can be overridden for any page/category/post. For
            instance, you can assign your own set of parameters for each
            category: color scheme, sidebars, menu type, style of slider and
            many more.
          </p>
          <button class="more-btn">more</button>
        </div>
        <div class="framework-info">
          <div class="framework-image">
            <div class="framework-graphic"></div>
          </div>
          <div class="framework-text">
            <p>
              TRX Framework It is a result of accumulated experience of
              WordPress themes development and constant contact with customers.
              It worked out an amazing combination of vast functionality and
              user's comfort. Thanks to an amazingly user friendly TRX
              Framework, customers have a chance to customize their theme the
              way they like.
            </p>
            <button class="more-btn">more</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-counter">
            <span class="counter-number">13</span>
            <span class="counter-label">psd</span>
          </div>
          <div class="stat-counter">
            <span class="counter-number">2</span>
            <span class="counter-label">drupal</span>
          </div>
          <div class="stat-counter">
            <span class="counter-number">10</span>
            <span class="counter-label">wp</span>
          </div>
          <div class="stat-counter">
            <span class="counter-number">7</span>
            <span class="counter-label">html</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Facts Section -->
    <section class="facts">
      <div class="container">
        <div class="facts-header">
          <h2>some facts about us</h2>
          <p>
            rock group gives you an option to create an infinite number of
            layout types.
          </p>
        </div>
        <div class="facts-skills">
          <div class="fact-skill">
            <div class="skill-circle" data-percent="85">
              <span class="skill-name">Css3</span>
            </div>
          </div>
          <div class="fact-skill">
            <div class="skill-circle" data-percent="90">
              <span class="skill-name">Html5</span>
            </div>
          </div>
          <div class="fact-skill">
            <div class="skill-circle" data-percent="75">
              <span class="skill-name">MySql</span>
            </div>
          </div>
          <div class="fact-skill">
            <div class="skill-circle" data-percent="80">
              <span class="skill-name">pHp</span>
            </div>
          </div>
          <div class="fact-skill">
            <div class="skill-circle" data-percent="95">
              <span class="skill-name">Design</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Purchase Section -->
    <section class="purchase">
      <div class="container">
        <h2>purchase</h2>
        <p>
          ThemeRex is doing their best to grant each owner of Rock Group with
          maximum opportunities to present their individuality, and establish
          the best contact with their audience.
        </p>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="logo-text">
              <span class="logo-main">rock group</span>
              <span class="logo-sub">infographic theme</span>
            </div>
          </div>
          <div class="footer-text">
            <p>
              ThemeREX © 2014. All Rights Reserved. Terms of Use and Privacy
              Policy.
            </p>
          </div>
          <div class="footer-social">
            <a href="#"><i class="fab fa-facebook"></i></a>
            <a href="#"><i class="fab fa-twitter"></i></a>
            <a href="#"><i class="fab fa-linkedin"></i></a>
          </div>
        </div>
      </div>
    </footer>

    <script src="rock-group.js"></script>
  </body>
</html>
