// Main Calendar Application
class BambooCalendar {
  constructor() {
    this.currentDate = new Date();
    this.currentPage = "daily";
    this.lunarCalendar = new LunarCalendar();
    this.events = this.loadEvents();
    this.scrollContainer = document.getElementById("scroll-container");

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.setupScrollSnapping();
    this.updateDailyView();
    this.updateMonthlyView();
  }

  setupEventListeners() {
    // Navigation buttons - now scroll to pages
    document.querySelectorAll(".nav-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        this.scrollToPage(e.target.dataset.page);
      });
    });

    // Daily navigation
    document.getElementById("prev-day").addEventListener("click", () => {
      this.changeDay(-1);
    });

    document.getElementById("next-day").addEventListener("click", () => {
      this.changeDay(1);
    });

    // Monthly navigation
    document.getElementById("prev-month").addEventListener("click", () => {
      this.changeMonth(-1);
    });

    document.getElementById("next-month").addEventListener("click", () => {
      this.changeMonth(1);
    });

    // Event modal
    document.querySelector(".add-event-btn").addEventListener("click", () => {
      this.showEventModal();
    });

    document.querySelector(".close-modal").addEventListener("click", () => {
      this.hideEventModal();
    });

    document.querySelector(".btn-cancel").addEventListener("click", () => {
      this.hideEventModal();
    });

    document.getElementById("event-form").addEventListener("submit", (e) => {
      e.preventDefault();
      this.saveEvent();
    });

    // Close modal on outside click
    document.getElementById("event-modal").addEventListener("click", (e) => {
      if (e.target.id === "event-modal") {
        this.hideEventModal();
      }
    });

    // Scroll indicators
    document.querySelectorAll(".scroll-indicator").forEach((indicator) => {
      indicator.addEventListener("click", () => {
        if (indicator.classList.contains("up")) {
          this.scrollToPage("daily");
        } else {
          this.scrollToPage("monthly");
        }
      });
    });

    // Keyboard navigation
    document.addEventListener("keydown", (e) => {
      if (e.key === "ArrowLeft") {
        if (this.currentPage === "daily") {
          this.changeDay(-1);
        } else {
          this.changeMonth(-1);
        }
      } else if (e.key === "ArrowRight") {
        if (this.currentPage === "daily") {
          this.changeDay(1);
        } else {
          this.changeMonth(1);
        }
      } else if (e.key === "ArrowUp") {
        this.scrollToPage("daily");
      } else if (e.key === "ArrowDown") {
        this.scrollToPage("monthly");
      } else if (e.key === "Escape") {
        this.hideEventModal();
      }
    });
  }

  setupScrollSnapping() {
    // Track current page based on scroll position
    let ticking = false;

    this.scrollContainer.addEventListener("scroll", () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.updateCurrentPage();
          ticking = false;
        });
        ticking = true;
      }
    });
  }

  updateCurrentPage() {
    const scrollTop = this.scrollContainer.scrollTop;
    const pageHeight = window.innerHeight - 80; // Account for navbar

    if (scrollTop < pageHeight / 2) {
      this.currentPage = "daily";
    } else {
      this.currentPage = "monthly";
    }

    // Update navigation active state
    document.querySelectorAll(".nav-btn").forEach((btn) => {
      btn.classList.remove("active");
    });
    document
      .querySelector(`[data-page="${this.currentPage}"]`)
      .classList.add("active");
  }

  scrollToPage(page) {
    const pageHeight = window.innerHeight - 80; // Account for navbar
    const targetScroll = page === "daily" ? 0 : pageHeight;

    this.scrollContainer.scrollTo({
      top: targetScroll,
      behavior: "smooth",
    });

    this.currentPage = page;
  }

  changeDay(delta) {
    this.currentDate.setDate(this.currentDate.getDate() + delta);
    this.updateDailyView();
  }

  changeMonth(delta) {
    this.currentDate.setMonth(this.currentDate.getMonth() + delta);
    this.updateMonthlyView();
  }

  updateDailyView() {
    const lunarInfo = this.lunarCalendar.getLunarInfo(this.currentDate);

    // Update solar date
    document.getElementById("solar-day").textContent =
      this.currentDate.getDate();
    document.getElementById("solar-month-year").textContent =
      this.formatSolarMonthYear(this.currentDate);
    document.getElementById("weekday").textContent = this.getWeekdayName(
      this.currentDate.getDay()
    );

    // Update lunar date
    document.getElementById("lunar-day").textContent = lunarInfo.lunar.day;
    document.getElementById("lunar-month-year").textContent =
      lunarInfo.monthYear;
    document.getElementById(
      "lunar-zodiac"
    ).textContent = `Năm ${lunarInfo.zodiac}`;

    // Update lunar info
    document.getElementById("can-chi").textContent = lunarInfo.canChi;
    document.getElementById("good-hours").textContent = lunarInfo.goodHours;
    document.getElementById("star").textContent = lunarInfo.star;

    // Update events
    this.updateDailyEvents();
  }

  updateMonthlyView() {
    const lunarInfo = this.lunarCalendar.getLunarInfo(this.currentDate);

    // Update month header
    document.getElementById("current-month").textContent =
      this.formatSolarMonthYear(this.currentDate);
    document.getElementById("lunar-month-display").textContent =
      lunarInfo.monthYear;

    // Generate calendar
    this.generateCalendar();

    // Update holidays
    this.updateMonthlyHolidays();
  }

  generateCalendar() {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();
    const today = new Date();

    const firstDay = new Date(year, month, 1);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const calendarDays = document.getElementById("calendar-days");
    calendarDays.innerHTML = "";

    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dayElement = this.createDayElement(currentDate, month, today);
      calendarDays.appendChild(dayElement);
    }
  }

  createDayElement(date, currentMonth, today) {
    const dayDiv = document.createElement("div");
    dayDiv.className = "calendar-day";

    if (date.getMonth() !== currentMonth) {
      dayDiv.classList.add("other-month");
    }

    if (this.isSameDay(date, today)) {
      dayDiv.classList.add("today");
    }

    const lunarInfo = this.lunarCalendar.getLunarInfo(date);

    dayDiv.innerHTML = `
            <div class="solar-day-num">${date.getDate()}</div>
            <div class="lunar-day-num">${lunarInfo.lunar.day}</div>
        `;

    dayDiv.addEventListener("click", () => {
      this.currentDate = new Date(date);
      this.scrollToPage("daily");
      this.updateDailyView();
    });

    return dayDiv;
  }

  updateDailyEvents() {
    const dateKey = this.formatDateKey(this.currentDate);
    const dayEvents = this.events[dateKey] || [];

    const eventsList = document.getElementById("daily-events");
    eventsList.innerHTML = "";

    dayEvents.forEach((event) => {
      const eventDiv = document.createElement("div");
      eventDiv.className = "event-item";
      eventDiv.innerHTML = `
                <span class="event-time">${event.time}</span>
                <span class="event-title">${event.title}</span>
            `;
      eventsList.appendChild(eventDiv);
    });

    if (dayEvents.length === 0) {
      eventsList.innerHTML =
        '<p style="color: #666; text-align: center; padding: 1rem; font-size: 0.9rem;">Không có sự kiện</p>';
    }
  }

  updateMonthlyHolidays() {
    const holidays = this.getMonthlyHolidays(this.currentDate);
    const holidaysList = document.getElementById("monthly-holidays");

    holidaysList.innerHTML = "";

    holidays.forEach((holiday) => {
      const holidayDiv = document.createElement("div");
      holidayDiv.className = "holiday-item";
      holidayDiv.innerHTML = `
                <span class="holiday-date">${holiday.date}</span>
                <span class="holiday-name">${holiday.name}</span>
            `;
      holidaysList.appendChild(holidayDiv);
    });

    if (holidays.length === 0) {
      holidaysList.innerHTML =
        '<p style="color: #666; text-align: center; padding: 1rem; font-size: 0.9rem;">Không có ngày lễ</p>';
    }
  }

  showEventModal() {
    document.getElementById("event-modal").classList.add("show");
    document.getElementById("event-title").focus();
  }

  hideEventModal() {
    document.getElementById("event-modal").classList.remove("show");
    document.getElementById("event-form").reset();
  }

  saveEvent() {
    const title = document.getElementById("event-title").value;
    const time = document.getElementById("event-time").value;
    const desc = document.getElementById("event-desc").value;

    if (!title || !time) return;

    const dateKey = this.formatDateKey(this.currentDate);
    if (!this.events[dateKey]) {
      this.events[dateKey] = [];
    }

    this.events[dateKey].push({
      title,
      time,
      description: desc,
      id: Date.now(),
    });

    this.saveEvents();
    this.updateDailyEvents();
    this.hideEventModal();
  }

  // Utility functions
  formatSolarMonthYear(date) {
    const months = [
      "Tháng 1",
      "Tháng 2",
      "Tháng 3",
      "Tháng 4",
      "Tháng 5",
      "Tháng 6",
      "Tháng 7",
      "Tháng 8",
      "Tháng 9",
      "Tháng 10",
      "Tháng 11",
      "Tháng 12",
    ];
    return `${months[date.getMonth()]}, ${date.getFullYear()}`;
  }

  getWeekdayName(dayIndex) {
    const weekdays = [
      "Chủ Nhật",
      "Thứ Hai",
      "Thứ Ba",
      "Thứ Tư",
      "Thứ Năm",
      "Thứ Sáu",
      "Thứ Bảy",
    ];
    return weekdays[dayIndex];
  }

  formatDateKey(date) {
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
  }

  isSameDay(date1, date2) {
    return (
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  }

  getMonthlyHolidays(date) {
    const month = date.getMonth() + 1;

    const holidays = {
      1: [
        { date: "1/1", name: "Tết Dương Lịch" },
        { date: "10/1", name: "Tết Nguyên Đán" }, // Example
      ],
      2: [{ date: "14/2", name: "Lễ Tình Nhân" }],
      3: [{ date: "8/3", name: "Quốc Tế Phụ Nữ" }],
      4: [{ date: "30/4", name: "Giải Phóng Miền Nam" }],
      5: [
        { date: "1/5", name: "Quốc Tế Lao Động" },
        { date: "19/5", name: "Sinh Nhật Bác Hồ" },
      ],
      6: [{ date: "1/6", name: "Quốc Tế Thiếu Nhi" }],
      9: [{ date: "2/9", name: "Quốc Khánh" }],
      10: [{ date: "20/10", name: "Ngày Phụ Nữ Việt Nam" }],
      11: [{ date: "20/11", name: "Ngày Nhà Giáo Việt Nam" }],
      12: [{ date: "25/12", name: "Giáng Sinh" }],
    };

    return holidays[month] || [];
  }

  loadEvents() {
    const saved = localStorage.getItem("bamboo-calendar-events");
    return saved ? JSON.parse(saved) : {};
  }

  saveEvents() {
    localStorage.setItem("bamboo-calendar-events", JSON.stringify(this.events));
  }
}

// Initialize the calendar when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  const calendar = new BambooCalendar();
  window.bambooCalendar = calendar; // Make it globally accessible if needed
});
