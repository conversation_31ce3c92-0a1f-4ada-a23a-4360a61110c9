/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
    height: 100vh;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.nav-menu {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    background: none;
    border: 2px solid transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
}

.nav-btn.active {
    background: #667eea;
    color: white;
}

/* Scroll Container - True One Page Scroll */
.scroll-container {
    height: 100vh;
    overflow-y: scroll;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    padding-top: 80px;
}

.page {
    height: calc(100vh - 80px);
    scroll-snap-align: start;
    scroll-snap-stop: always;
    display: flex;
    flex-direction: column;
}

.page-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
}

/* Daily Calendar Styles - Compact */
.daily-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.nav-arrow {
    background: #667eea;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-arrow:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.date-display {
    text-align: center;
    display: flex;
    gap: 2rem;
    align-items: center;
}

.main-date h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.date-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.date-meta span {
    font-size: 1rem;
    color: #666;
}

.lunar-date {
    border-left: 2px solid #e0e0e0;
    padding-left: 2rem;
}

.lunar-date h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #d4af37;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.lunar-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.lunar-meta span {
    font-size: 0.9rem;
    color: #888;
}

/* Daily Content Grid - Compact */
.daily-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    flex: 1;
    margin: 1.5rem 0;
}

.info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: fit-content;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.card-header i {
    font-size: 1.2rem;
    color: #667eea;
}

.card-content {
    flex: 1;
}

/* Weather Card */
.weather-card .card-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.temperature {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.weather-desc {
    color: #666;
    font-size: 1rem;
}

/* Lunar Card */
.lunar-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.lunar-item:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    color: #666;
    font-size: 0.9rem;
}

.value {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

/* Events Card */
.events-card {
    grid-column: 1 / -1;
}

.events-card .card-header {
    justify-content: space-between;
}

.events-card .add-event-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.events-card .add-event-btn:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 120px;
    overflow-y: auto;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

.event-time {
    font-weight: 600;
    color: #667eea;
    min-width: 50px;
    font-size: 0.9rem;
}

.event-title {
    flex: 1;
    color: #333;
    font-size: 0.9rem;
}

/* Zodiac Card */
.zodiac-display {
    text-align: center;
    margin-bottom: 1rem;
}

.zodiac-animal {
    font-size: 1.5rem;
    font-weight: 700;
    color: #d4af37;
    display: block;
}

.zodiac-year {
    font-size: 0.9rem;
    color: #666;
}

.star-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.75rem;
    border-top: 1px solid #f0f0f0;
}

/* Scroll Indicators */
.scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    animation: bounce 2s infinite;
    flex-shrink: 0;
    margin-top: auto;
}

.scroll-indicator.up {
    margin-top: 0;
    margin-bottom: auto;
}

.scroll-indicator i {
    font-size: 1.5rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Monthly Calendar Styles - Compact */
.monthly-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.month-display {
    text-align: center;
}

.month-display h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
    line-height: 1;
}

#lunar-month-display {
    font-size: 1rem;
    color: #d4af37;
    font-weight: 500;
}

/* Calendar Wrapper */
.calendar-wrapper {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 1.5rem;
    flex: 1;
    margin: 1.5rem 0;
}

.calendar-grid {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 1rem;
}

.weekday {
    text-align: center;
    font-weight: 600;
    color: #667eea;
    padding: 0.75rem 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 0.9rem;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.25rem;
    min-height: 60px;
}

.calendar-day:hover {
    background: #f0f4ff;
    transform: scale(1.05);
}

.calendar-day.today {
    background: #667eea;
    color: white;
}

.calendar-day.other-month {
    color: #ccc;
}

.solar-day-num {
    font-size: 1rem;
    font-weight: 600;
}

.lunar-day-num {
    font-size: 0.7rem;
    color: #d4af37;
    margin-top: 0.25rem;
}

.calendar-day.today .lunar-day-num {
    color: rgba(255, 255, 255, 0.8);
}

/* Month Sidebar */
.month-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.holidays-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.holidays-card h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
    font-size: 1rem;
}

.holidays-card h3 i {
    color: #ff6b6b;
}

.holidays-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
}

.holiday-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #fff5f5;
    border-radius: 8px;
    border-left: 3px solid #ff6b6b;
}

.holiday-date {
    font-weight: 600;
    color: #ff6b6b;
    min-width: 50px;
    font-size: 0.9rem;
}

.holiday-name {
    flex: 1;
    color: #333;
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-header h3 {
    color: #333;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: #f0f0f0;
    color: #333;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-cancel,
.btn-save {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #f0f0f0;
    color: #666;
}

.btn-cancel:hover {
    background: #e0e0e0;
}

.btn-save {
    background: #667eea;
    color: white;
}

.btn-save:hover {
    background: #5a6fd8;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .calendar-wrapper {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .month-sidebar {
        order: -1;
    }

    .holidays-card {
        max-height: 200px;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .page-content {
        padding: 1rem;
    }

    .daily-header,
    .monthly-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .date-display {
        flex-direction: column;
        gap: 1rem;
    }

    .lunar-date {
        border-left: none;
        border-top: 2px solid #e0e0e0;
        padding-left: 0;
        padding-top: 1rem;
    }

    .daily-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .main-date h1 {
        font-size: 2.5rem;
    }

    .lunar-date h2 {
        font-size: 1.5rem;
    }

    .calendar-grid {
        padding: 1rem;
    }

    .weekday {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    .calendar-day {
        min-height: 50px;
        padding: 0.2rem;
    }

    .solar-day-num {
        font-size: 0.9rem;
    }

    .lunar-day-num {
        font-size: 0.6rem;
    }

    .scroll-indicator {
        font-size: 0.8rem;
    }

    .scroll-indicator i {
        font-size: 1.2rem;
    }
}
