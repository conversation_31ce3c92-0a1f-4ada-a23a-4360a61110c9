/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
    height: 100vh;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.nav-menu {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    background: none;
    border: 2px solid transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
}

.nav-btn.active {
    background: #667eea;
    color: white;
}

/* Scroll Container */
.scroll-container {
    height: 100vh;
    overflow: hidden;
    padding-top: 80px;
}

.page {
    height: calc(100vh - 80px);
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
    width: 100%;
    top: 80px;
    left: 0;
}

.page.active {
    opacity: 1;
    transform: translateY(0);
    position: relative;
}

.page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    height: 100%;
    overflow-y: auto;
}

/* Daily Calendar Styles */
.daily-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.nav-arrow {
    background: #667eea;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-arrow:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.date-display {
    text-align: center;
    display: flex;
    gap: 3rem;
    align-items: center;
}

.solar-date h1 {
    font-size: 4rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.date-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.date-info span {
    font-size: 1.1rem;
    color: #666;
}

.lunar-date {
    border-left: 2px solid #e0e0e0;
    padding-left: 3rem;
}

.lunar-date h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #d4af37;
    margin-bottom: 0.5rem;
}

.lunar-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.lunar-info span {
    font-size: 1rem;
    color: #888;
}

/* Daily Content */
.daily-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.weather-card,
.lunar-info-card,
.events-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.events-card {
    grid-column: 1 / -1;
}

.weather-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.weather-icon {
    font-size: 3rem;
    color: #ffa500;
}

.temperature {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
}

.weather-desc {
    color: #666;
    font-size: 1.1rem;
}

.lunar-info-card h3,
.events-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
    font-weight: 600;
}

.lunar-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.lunar-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.lunar-item:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    color: #666;
}

.value {
    font-weight: 600;
    color: #333;
}

/* Events */
.events-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.event-time {
    font-weight: 600;
    color: #667eea;
    min-width: 60px;
}

.event-title {
    flex: 1;
    color: #333;
}

.add-event-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.add-event-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

/* Monthly Calendar Styles */
.monthly-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.month-display {
    text-align: center;
}

.month-display h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

#lunar-month-display {
    font-size: 1.2rem;
    color: #d4af37;
    font-weight: 500;
}

/* Calendar Grid */
.calendar-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 1rem;
}

.weekday {
    text-align: center;
    font-weight: 600;
    color: #667eea;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem;
}

.calendar-day:hover {
    background: #f0f4ff;
    transform: scale(1.05);
}

.calendar-day.today {
    background: #667eea;
    color: white;
}

.calendar-day.other-month {
    color: #ccc;
}

.solar-day-num {
    font-size: 1.2rem;
    font-weight: 600;
}

.lunar-day-num {
    font-size: 0.8rem;
    color: #d4af37;
    margin-top: 0.25rem;
}

.calendar-day.today .lunar-day-num {
    color: rgba(255, 255, 255, 0.8);
}

/* Month Info */
.month-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

.holidays-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.holidays-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.holiday-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #fff5f5;
    border-radius: 10px;
    border-left: 4px solid #ff6b6b;
}

.holiday-date {
    font-weight: 600;
    color: #ff6b6b;
    min-width: 60px;
}

.holiday-name {
    flex: 1;
    color: #333;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-header h3 {
    color: #333;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: #f0f0f0;
    color: #333;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-cancel,
.btn-save {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #f0f0f0;
    color: #666;
}

.btn-cancel:hover {
    background: #e0e0e0;
}

.btn-save {
    background: #667eea;
    color: white;
}

.btn-save:hover {
    background: #5a6fd8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }
    
    .page-content {
        padding: 1rem;
    }
    
    .daily-header,
    .monthly-header {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .date-display {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .lunar-date {
        border-left: none;
        border-top: 2px solid #e0e0e0;
        padding-left: 0;
        padding-top: 1.5rem;
    }
    
    .daily-content {
        grid-template-columns: 1fr;
    }
    
    .solar-date h1 {
        font-size: 3rem;
    }
    
    .lunar-date h2 {
        font-size: 2rem;
    }
    
    .weekday,
    .calendar-day {
        padding: 0.5rem 0.25rem;
    }
    
    .solar-day-num {
        font-size: 1rem;
    }
    
    .lunar-day-num {
        font-size: 0.7rem;
    }
}
